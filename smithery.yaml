# Smithery configuration file: https://smithery.ai/docs/config#smitheryyaml

startCommand:
  type: stdio
  configSchema:
    # JSON Schema defining the configuration options for the MCP.
    type: object
    required:
      - allowCommands
    properties:
      allowCommands:
        type: string
        description: Comma-separated list of shell commands that are allowed to be executed.
  commandFunction:
    # A function that produces the CLI command to start the MCP on stdio.
    |-
    (config) => ({ command: 'python', args: ['-m', 'mcp_shell_server.server'], env: { ALLOW_COMMANDS: config.allowCommands } })