name: Test

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"]

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install uv
        run: |
          python -m pip install --upgrade pip
          pip install uv

      - name: Install dependencies
        run: |
          uv venv
          uv pip install -e ".[dev,test]"

      - name: Run format checks and typecheck
        run: |
          uv run make check

      - name: Run tests with coverage
        run: |
          uv run make coverage

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v5
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
