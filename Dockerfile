# Generated by https://smithery.ai. See: https://smithery.ai/docs/config#dockerfile
# Use a Python image with uv pre-installed
FROM ghcr.io/astral-sh/uv:python3.11-bookworm-slim AS uv

# Set the working directory
WORKDIR /app

# Copy the project files
COPY . .

# Install the project's dependencies
RUN --mount=type=cache,target=/root/.cache/uv pip install .

# Set environment variables
ENV ALLOW_COMMANDS="ls,cat,pwd,grep,wc,touch,find"

# Start the server using the local script
ENTRYPOINT ["python", "-m", "mcp_shell_server.server"]
