name: Publish

on:
  push:
    tags:
      - "v*"

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"]

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install uv
        run: |
          python -m pip install --upgrade pip
          pip install uv

      - name: Install dependencies
        run: |
          uv venv
          uv pip install -e ".[dev,test]"

      - name: Run tests and checks
        run: |
          uv run make all

  publish:
    needs: test
    runs-on: ubuntu-latest
    environment: release
    permissions:
      id-token: write

    steps:
      - uses: actions/checkout@v4

      - name: Update version from tag
        run: |
          VERSION=${GITHUB_REF#refs/tags/v}
          echo "__version__ = \"${VERSION}\"" > src/mcp_shell_server/version.py

      - name: Set up Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Install uv
        run: |
          python -m pip install --upgrade pip
          pip install uv

      - name: Build package
        run: |
          uv venv
          uv pip install build
          uv run python -m build

      - name: Publish to PyPI
        uses: pypa/gh-action-pypi-publish@release/v1
        with:
          password: ${{ secrets.PYPI_TOKEN }}
