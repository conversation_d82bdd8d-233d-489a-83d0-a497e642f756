[project]
name = "mcp-shell-server"
description = "MCP Shell Server - Execute shell commands via MCP protocol"
authors = [
    { name = "tumf" }
]
dependencies = [
    "asyncio>=3.4.3",
    "mcp>=1.1.2",
]
requires-python = ">=3.11"
readme = "README.md"
license = { text = "MIT" }
dynamic = ["version"]

[project.scripts]
mcp-shell-server = "mcp_shell_server:main"

[project.optional-dependencies]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.23.0", 
    "pytest-env>=1.1.0",
    "pytest-cov>=6.0.0",
    "pytest-mock>=3.12.0",
]
dev = [
    "ruff>=0.0.262",
    "black>=23.3.0",
    "isort>=5.12.0",
    "mypy>=1.2.0",
    "pre-commit>=3.2.2",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.pytest.ini_options]
asyncio_mode = "strict"
testpaths = "tests"
# Set default event loop scope for async tests 
asyncio_default_fixture_loop_scope = "function"
markers = [
    "macos: marks tests that should only run on macOS",
    "slow: marks tests as slow running",
]
filterwarnings = [
    "ignore::RuntimeWarning:selectors:",
    "ignore::pytest.PytestUnhandledCoroutineWarning:",
    "ignore::pytest.PytestUnraisableExceptionWarning:",
    "ignore::DeprecationWarning:pytest_asyncio.plugin:",
]

[tool.ruff]
lint.select = [
    "E",  # pycodestyle errors
    "F",  # pyflakes
    "W",  # pycodestyle warnings
    "I",  # isort
    "C",  # flake8-comprehensions
    "B",  # flake8-bugbear
]
lint.ignore = [
    "E501",  # line too long, handled by black
    "B008",  # do not perform function calls in argument defaults
    "C901",  # too complex
]

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black" 
line_length = 88

[tool.hatch.version]
path = "src/mcp_shell_server/version.py"

[tool.hatch.build.targets.wheel]
packages = ["src/mcp_shell_server"]

[tool.hatch.metadata]
allow-direct-references = true


[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise NotImplementedError",
    "if __name__ == .__main__.:",
    "pass",
    "raise ImportError",
    "__version__",
    "except IOError:",
    "except IOError as e:",
    "def _cleanup_handles",
    "def __aexit__",
    "if path in [\">\", \">>\", \"<\"]:",
    "def _close_handles",
]

omit = [
    "src/mcp_shell_server/__init__.py",
    "src/mcp_shell_server/version.py",
]
